import sys
from cx_Freeze import setup, Executable

# 依赖包
build_exe_options = {
    "packages": [
        "telegram", 
        "requests", 
        "schedule", 
        "logging", 
        "threading", 
        "json", 
        "time",
        "collections",
        "asyncio"
    ],
    "excludes": ["tkinter"],  # 排除不需要的包
    "include_files": [
        "config.json",  # 包含配置文件
        # 如果有其他资源文件，也可以在这里添加
    ]
}

# 目标平台
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 不显示控制台窗口，如果需要调试可以设置为None

setup(
    name="SolanaTgBot",
    version="1.0",
    description="Solana Telegram Bot",
    options={"build_exe": build_exe_options},
    executables=[Executable("main.py", base=base, target_name="SolanaTgBot.exe")]
)
