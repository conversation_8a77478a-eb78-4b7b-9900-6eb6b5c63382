import os
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
import logging
import json
from CloudflareBypasser import CloudflareBypasser
import time
import requests
import random

# 缓存绕过5秒盾的Cookies
cached_cookies = None
last_bypass_time = 0

def get_chromium_options(browser_path: str, arguments: list):
    """
    配置并返回 Chromium 浏览器选项。
    """
    options = ChromiumOptions()
    options.set_paths(browser_path=browser_path)
    for argument in arguments:
        options.set_argument(argument)
    return options

def fetch_page_data(url):
    """
    绕过 Cloudflare 并提取页面数据。
    """
    global cached_cookies, last_bypass_time

    # 判断是否为无头模式
    is_headless = os.getenv('HEADLESS', 'false').lower() == 'true'
    if is_headless:
        from pyvirtualdisplay import Display
        display = Display(visible=0, size=(1920, 1080))
        display.start()

    # 设置 Chromium 浏览器路径
    browser_path = os.getenv('CHROME_PATH', "/usr/bin/google-chrome")

    # 浏览器启动参数
    arguments = [
        "-no-first-run",
        "-force-color-profile=srgb",
        "-metrics-recording-only",
        "-password-store=basic",
        "-use-mock-keychain",
        "-no-default-browser-check",
        "-disable-background-mode",
        "-enable-features=NetworkService,NetworkServiceInProcess",
        "-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",
        "-deny-permission-prompts",
        "-disable-gpu",
        "-accept-lang=en-US",
    ]

    # 配置 Chromium 浏览器
    options = get_chromium_options(browser_path, arguments)

    # 初始化浏览器
    driver = ChromiumPage(addr_or_opts=options)

    try:
        logging.info('Navigating to the demo page.')
        driver.get(url)

        # 开始绕过 Cloudflare
        logging.info('Starting Cloudflare bypass.')
        cf_bypasser = CloudflareBypasser(driver)
        cf_bypasser.bypass()

        # 缓存Cookies
        cached_cookies = driver.cookies()  # 使用 cookies 属性获取Cookies
        last_bypass_time = time.time()
         # 打印 Cookies 的值
        if cached_cookies:
            logging.info("Cookies retrieved successfully: %s", cached_cookies)
        else:
            logging.warning("No cookies were retrieved.")
        # 成功绕过后获取页面内容
        logging.info("Enjoy the content!")
        logging.info("Title of the page: %s", driver.title)

        # 从 driver.html 提取页面 HTML
        page_source = driver.html

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(page_source, 'html.parser')

        # 查找 <pre> 标签，提取 JSON 数据
        pre_content = soup.find('pre')
        if pre_content:
            json_data = pre_content.text  # 提取 JSON 文本
            parsed_data = json.loads(json_data)  # 解析 JSON 数据
            logging.info("JSON data extracted successfully.")
            return parsed_data
        else:
            logging.error("Failed to extract JSON data.")
            return None
    finally:
        logging.info("Closing the browser.")
        driver.quit()
        if is_headless:
            display.stop()




def convert_cookies_for_requests(cookies):
    """
    将 DrissionPage 的 Cookies 转换为 requests 库兼容的格式。
    """
    logging.info("Converting cookies for requests.")
    converted_cookies = {}
    for cookie in cookies:
        if cookie['name'] in ['__cf_bm', 'cf_clearance']:
            converted_cookies[cookie['name']] = cookie['value']
    return converted_cookies

def fetch_page_data_with_session(url):
    global cached_cookies, last_bypass_time

    # 检查缓存的 Cookies 是否有效
    if cached_cookies and time.time() - last_bypass_time < 600:  # 缓存10分钟
        convert_cookies = convert_cookies_for_requests(cached_cookies)
        logging.info("Using cached cookies to bypass Cloudflare.")

        # 构建请求的 Cookies
        cookies = {
            '_ga': 'GA1.1.1925278545.1722307768',
            '__cf_bm': convert_cookies.get('__cf_bm'),
            '_ga_0XM0LYXGC8': 'GS1.1.1739090983.10.1.1739092880.0.0.0',
            'cf_clearance': convert_cookies.get('cf_clearance')
        }  
        # 请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Referer': f'{url}'
        }

        # 发送请求
        response = requests.get(url, headers=headers, cookies=cookies)
        logging.info("Response status code: %s", response.status_code)

        if response.status_code == 200:
            return response.json()
        else:
            logging.error("Failed to fetch data with cached cookies. Status code: %s", response.status_code)
            # 如果返回403，清除缓存并重新获取数据
            cached_cookies = None
            return fetch_page_data(url)
    else:
        logging.info("Fetching data using browser.")
        data = fetch_page_data(url)
        return data


# # 使用示例
# url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
# data = fetch_page_data(url)
# print(data)