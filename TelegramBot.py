import logging
import threading
from time import sleep
import schedule
from collections import OrderedDict
from telegram.ext import Updater, CommandHandler
from cloudflare_bypass_helper import fetch_page_data
from gmgn_fetcher import filter_tokens, format_tokens, filter_bluechip_tokens, format_bluechip_tokens,ca_format_tg_message

GMGN_API_URL = "https://gmgn.ai/defi/quotation/v1/tokens/sol"
BLUECHIP_API_URL = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=100"

class TelegramBot:
    def __init__(self, token):
        self.token = token
        self.scheduler_running = False
        self.sent_tokens_cache = OrderedDict()
        self.updater = Updater(token)
        self.dispatcher = self.updater.dispatcher
        self.dispatcher.add_handler(CommandHandler("start", self.start))
        self.dispatcher.add_handler(CommandHandler("pushing", self.pushing))
        self.dispatcher.add_handler(CommandHandler("JK", self.start_schedule))
        self.dispatcher.add_handler(CommandHandler("ca", self.handle_ca))  # 添加 CA 命令处理器

    def start(self, update, context):
        self.chat_id = update.effective_chat.id
        update.message.reply_text(
            "你好！\n"
            "发送 /pushing 开始推送代币数据。\n"
            "发送 /JK 开启定时任务，每 5 分钟推送蓝筹代币数据。"
        )

    def pushing(self, update, context):
        """
        手动获取并发送代币数据
        """
        tokens_data = fetch_page_data(GMGN_API_URL)

        if not tokens_data or "data" not in tokens_data or "tokens" not in tokens_data["data"]:
            update.message.reply_text("未能获取代币数据。")
            return

        tokens = tokens_data["data"]["tokens"]
        filtered_tokens = filter_tokens(
            tokens,
            min_liquidity=120000,
            min_volume=200000,
            max_holders_rate=0.2  # 最大持币比例
        )
        message = format_tokens(filtered_tokens)

        if message:
            update.message.reply_text(message, parse_mode="HTML")
        else:
            update.message.reply_text("没有符合条件的代币。")
    

    def handle_ca(self, update, context):
        """
        处理接收到的 CA 命令
        """
        if not context.args:
            update.message.reply_text("请提供合约地址 (CA)。")
            return
        
        ca = context.args[0]
        update.message.reply_text(f"正在查询合约地址: {ca}...")
        
        url = f"https://gmgn.ai/_next/data/_pLFFWLu7OQ0hfgtjCi3I/sol/token/{ca}.json?chain=sol&token={ca}"
        # https://gmgn.ai/_next/data/YLjtfKhEJz3dq3J_ZVFzt/sol/token/FZRyRpkY8B7BHjGGYrFxXg3TfRqVXMTKBhK5hSdLpump.json?chain=sol&token=FZRyRpkY8B7BHjGGYrFxXg3TfRqVXMTKBhK5hSdLpump
        # 查询代币信息的逻辑
        response = fetch_page_data(url)
        
        if response and "pageProps" in response:
            token_info = response["pageProps"].get("tokenInfo", {})
            # 打印 tokens 数据以确认结构
            logging.info(f"获取的蓝筹代币数据: {token_info}")

        # 格式化为 Telegram 消息
        message = ca_format_tg_message(token_info)
        if message:
            update.message.reply_text(message, parse_mode="HTML")
        else:
            update.message.reply_text("未能获取代币信息，请检查合约地址。")

    def start_schedule(self, update, context):
        """
        启动定时任务
        """
        self.chat_id = update.effective_chat.id
        if not self.scheduler_running:
            update.message.reply_text("已开启定时任务，每 5 分钟推送一次蓝筹代币数据。")
            self.scheduler_running = True
            thread = threading.Thread(target=self.run_schedule, daemon=True)
            thread.start()
        else:
            update.message.reply_text("定时任务已经在运行中。")

    def run_schedule(self):
        """
        定时任务逻辑，每 5 分钟调用 fetch_and_send_bluechip_data
        """
        schedule.every(2).minutes.do(self.fetch_and_send_bluechip_data)
        schedule.every(1).hour.do(self.cleanup_cache)  # 每小时清理缓存
        while self.scheduler_running:
            schedule.run_pending()
            sleep(1)

    def fetch_and_send_bluechip_data(self):
        """
        获取蓝筹代币数据并发送到 Telegram
        """
        response = fetch_page_data(BLUECHIP_API_URL)

        if not response or "data" not in response:
            logging.error("未能获取到蓝筹代币数据。")
            return

        tokens = response["data"]
        filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
        new_tokens = [token for token in filtered_tokens if token['address'] not in self.sent_tokens_cache]

        for token in new_tokens:
            self.sent_tokens_cache[token['address']] = True

        if new_tokens:
            grouped_messages = self.group_messages(format_bluechip_tokens(new_tokens), group_size=1)
            self.send_message_with_retry(grouped_messages)
        else:
            logging.info("没有新的符合条件的代币需要发送。")

    def cleanup_cache(self):
        """
        清理缓存，确保缓存大小不超过 500 条
        """
        max_cache_size = 500
        if len(self.sent_tokens_cache) > max_cache_size:
            logging.info("清理缓存，当前大小: %d", len(self.sent_tokens_cache))
            while len(self.sent_tokens_cache) > max_cache_size:
                self.sent_tokens_cache.popitem(last=False)  # 移除最早的记录
            logging.info("缓存清理完成，当前大小: %d", len(self.sent_tokens_cache))

    def group_messages(self, tokens: list, group_size: int) -> list:
        """
        将代币数据分组，每组包含最多 group_size 个代币
        """
        grouped_messages = []
        for i in range(0, len(tokens), group_size):
            group = tokens[i:i + group_size]
            message = "\n\n".join(group)
            grouped_messages.append(message)
        return grouped_messages

    def send_message_with_retry(self, messages: list, retries=3, delay=5):
        """
        循环发送多条消息并在失败时重试。
        """
        for message in messages:
            if not message.strip():  # 检查消息是否为空
                logging.error("跳过空消息。")
                continue
            for attempt in range(retries):
                try:
                    self.updater.bot.send_message(
                        chat_id=self.chat_id,
                        text=message,
                        parse_mode="HTML",
                        timeout=30
                    )
                    logging.info("消息发送成功。")
                    break
                except Exception as e:
                    logging.error(f"发送消息时发生错误 (尝试 {attempt + 1}/{retries}): {e}")
                    sleep(delay)
            else:
                logging.error("所有重试均失败，消息未能发送。")


    def run(self):
        self.updater.start_polling()
        self.updater.idle()