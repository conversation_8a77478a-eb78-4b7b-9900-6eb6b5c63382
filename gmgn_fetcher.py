import pandas as pd
from datetime import datetime, timedelta
import logging
from html import escape
from cloudflare_bypass_helper import fetch_page_data
from collections import defaultdict

def filter_tokens(tokens, min_liquidity, min_volume, max_holders_rate):
    """
    根据筛选条件过滤代币。
    :param tokens: 代币列表。
    :param min_liquidity: 最小流动性。
    :param min_volume: 最小交易量。
    :param max_holders_rate: 最大前 10 持有人比例。
    :return: 筛选后的代币列表。
    """
    if not tokens:
        return []

    # 转换为 DataFrame
    df = pd.DataFrame(tokens)

    # 检查必要字段是否存在
    required_columns = ['liquidity', 'volume_24h', 'top_10_holder_rate']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必要的字段: {col}")

    # 应用筛选条件
    filtered_df = df[
        (df['liquidity'] >= min_liquidity) &
        (df['volume_24h'] >= min_volume) &
        (df['top_10_holder_rate'] <= max_holders_rate)
    ]

    # 转换回列表返回
    return filtered_df.to_dict('records')





def format_tokens(filtered_tokens):
    """
    格式化筛选后的代币信息（带 K 和 M）。
    :param filtered_tokens: 筛选后的代币列表。
    :return: 格式化后的字符串。
    """
    if not filtered_tokens:  # 如果列表为空
        return "没有符合条件的代币。"

    formatted_tokens = []
    for token in filtered_tokens:
        formatted_tokens.append(
            f"<b>代币符号:</b> {token['symbol']}\n"
            f"<b>代币名称:</b> {token['name']}\n"
            f"<b>代币地址:</b> <code>{token['address']}</code>\n"
            f"<b>当前价格:</b> ${token['price']:.6f}\n"
            f"<b>流动性:</b> ${format_large_number(token['liquidity'])}\n"
            f"<b>24小时交易量:</b> ${format_large_number(token['volume_24h'])}\n"
            f"<b>前10持币比例:</b> {token['top_10_holder_rate'] * 100:.2f}%\n"
        )

    return "\n\n".join(formatted_tokens)

def format_large_number(value):
    """
    格式化数字为简短的 K/M/B 形式
    :param value: 数字
    :return: 格式化后的字符串
    """
    try:
        value = float(value)
        if value >= 1_000_000_000:
            return f"{value / 1_000_000_000:.2f}B"
        elif value >= 1_000_000:
            return f"{value / 1_000_000:.2f}M"
        elif value >= 1_000:
            return f"{value / 1_000:.2f}K"
        return f"{value:.2f}"
    except Exception as e:
        logging.error(f"格式化数字时出错: {e}")
        return "N/A"

def format_bluechip_tokens(tokens):
    """
    格式化代币信息以发送到 Telegram。
    """
    messages = []
    for token in tokens:
        try:
            # 使用 escape 确保内容不包含非法字符
            address = escape(token.get('address', '无地址'))
            gmgn_link = escape(token.get('token_links', {}).get('gmgn', ''))
            symbol = escape(token.get('symbol', '未知'))
            name = escape(token.get('name', '无名称'))
            twitter_username = escape(token["token_links"].get("twitter_username", "无推特"))
            telegram = escape(token["token_links"].get("telegram", "无TG"))
            website = escape(token["token_links"].get("website", "无网站"))
            # Ensure these are numbers (convert string values to float)
            price = float(token.get('price', 0))  # Convert price to float
            price_change_1m = float(token.get('price_change_percent_1m', 0))  # Convert to float
            price_change_5m = float(token.get('price_change_percent_5m', 0))  # Convert to float
            price_change_1h = float(token.get('price_change_percent_1h', 0))  # Convert to float
            holder_count = int(token.get('holder_count', 0))  # Convert to int
            liquidity = float(token.get('liquidity', 0))  # Convert to float
            market_cap = float(token.get('market_cap', 0))  # Convert to float
            buys = int(token.get('buys', 0))  # Convert to int
            sells = int(token.get('sells', 0))  # Convert to int
            volume = float(token.get('volume', 0))  # Convert to float
            top_10_holder_rate = float(token.get('security_info', {}).get('top_10_holder_rate', 0))
            # 提取安全信息
            security_info = token.get('security_info', {})
            renounced_freeze_account = security_info.get('renounced_freeze_account', 0)
            renounced_mint = security_info.get('renounced_mint', 0)
            burn_status = security_info.get('burn_status', 'not_burn')
            
            # 将字符串转换为浮点数或整数
            dev_token_burn_amount = float(security_info.get('dev_token_burn_amount', 0))  # 转换为浮点数
            dev_token_burn_ratio = float(security_info.get('dev_token_burn_ratio', 0))    # 转换为浮点数
            
            # 格式化安全信息
            security_message = (
                f"<b>安全信息:</b>\n"
                f"- 冻结权限是否丢弃: {'是' if renounced_freeze_account == 1 else '否'}\n"
                f"- 铸币权限是否丢弃: {'是' if renounced_mint == 1 else '否'}\n"
                f"- 是否烧池子: {'是' if burn_status == 'burn' else '否'}\n"
                f"- 开发者代币销毁数量: {dev_token_burn_amount:.2f}\n"  # 使用浮点数格式化
                f"- 开发者代币销毁比例: {dev_token_burn_ratio * 100:.2f}%\n"  # 使用浮点数格式化
            )
            print(f"处理代币:3")
            # 获取 Rug 文案
            rug_message = fetch_and_process_rug_dataNew(address)
            # 如果 Rug 数据为 None，表示需要过滤掉该代币
            if rug_message is None:
                continue

            # 获取聪明钱包数据
            smart_wallet_message = fetch_and_process_smart_wallet(address)
            # 检查市值是否小于1M，如果是则标红
            if market_cap < 1_000_000:
                market_cap_message = f"$✪✪✪{format_large_number(market_cap)}✪✪✪"
            else:
                market_cap_message = f"${format_large_number(market_cap)}"
              # 准备格式化消息
            twitter_link = f"https://x.com/{twitter_username}" if twitter_username != "无推特" else "无推特"
            pump_news_link = f"https://www.pump.news/zh/{address}-solana"
            # Prepare the formatted message with proper number formatting
            message = (
                f"<b>币种:</b> {symbol}\n"
                f"<b>地址:</b> <a href='{gmgn_link}'>{address}</a>\n"
                f"<b>CA:</b> <code>{address}</code>\n"  # 让地址变得可以复制
                f"<b>价格:</b> ${price:.6f}\n"
                f"<b>1分钟涨跌幅:</b> {price_change_1m}%\n"
                f"<b>5分钟涨跌幅:</b> {price_change_5m}%\n"
                f"<b>1小时涨跌幅:</b> {price_change_1h}%\n"
                f"<b>持币地址数:</b> {holder_count}\n"
                f"<b>流动池:</b> ${liquidity}\n"
                f"<b>市值:</b> {market_cap_message}\n"  # 如果市值小于1M，显示红色
                f"<b>买卖笔数:</b> {buys} 买 / {sells} 卖\n"
                f"<b>1分钟交易量:</b> ${volume}\n"
                f"<b>前10持币比例:</b> {top_10_holder_rate * 100:.2f}%\n"
                f"<b>推特:</b> <a href='{twitter_link}'>{twitter_username}</a> (点击复制)\n"
                f"<b>TG:</b> ${telegram}\n"
                f"<b>网站:</b> ${website}\n"
                f"<b>pump分析地址:</b> <a href='{pump_news_link}'>{pump_news_link}</a> (点击复制)\n"
                f"{security_message}"
                f"{rug_message}"
                f"{smart_wallet_message}"  # 添加聪明钱包的交易信息       
            )
            messages.append(message)
        except Exception as e:
            logging.error(f"格式化代币信息时出错: {e}")

    return messages



def fetch_and_process_rug_dataNew(address):
    """
    获取并处理 Rug 数据。
    :param address: 代币地址
    :return: 代币的 Rug 信息
    """
    rug_url = f"https://gmgn.ai/defi/quotation/v1/tokens/rug_history/sol/{address}"
    rug_response = fetch_page_data(rug_url)
    logging.info(f"获取rug 数据成功 {rug_response}")
    if not rug_response or "data" not in rug_response:
        logging.warning(f"没有找到地址 {address} 的 Rug 数据")
        return None
    
    rug_data = rug_response["data"]
    history = rug_data.get("history", [])
    
    # 使用 history 的长度作为跑路次数
    holder_rugged_num = len(history)

    rug_message = (
        f"<b>跑路次数:</b> {holder_rugged_num} 次\n"
    )
    
    return rug_message

def fetch_and_process_rug_data(address):
    """
    根据代币地址调用 `devrug` 接口并处理响应数据
    :param address: 代币地址
    :return: 格式化后的 Rug 文案
    """
    devrug_url = f"https://gmgn.ai/defi/quotation/v1/tokens/rug_history/sol/{address}"
    logging.info(f"调用 Rug 数据接口: {devrug_url}")
    response = fetch_page_data(devrug_url)
    logging.info(f"获取rug 数据成功 {response}")
    if response and "data" in response:
        rug_data = response["data"]
        history = rug_data.get("history", [])
        # 如果跑路次数大于 3 次，则过滤掉该代币
        if len(history) > 3:
            logging.info(f"代币 {address} 有超过 3 次跑路记录，已被过滤。")
            return None
        # 生成 Rug 文案
        if not history:
            rug_message = f"<b>Rug 状态:</b> 首次发币，无跑路记录。\n"
        else:
            history_message = []
            for record in history:
                rugged_type = record.get("rugged_type", "未知类型")
                rugged_tokens = record.get("rugged_tokens", "无记录")
                rugged_token_address = record.get("token_address", "无地址")
                rugged_name = record.get("name", "无名称")

                history_message.append(
                    f"- 名称: {rugged_name}\n"
                    f"  类型: {rugged_type}\n"
                    f"  地址: {rugged_token_address}\n"
                    f"  涉及代币: {rugged_tokens}"
                )
            rug_message = (
                f"<b>Rug 状态:</b> 有跑路记录。\n"
                f"<b>跑路次数:</b> {len(history)}\n"
                f"<b>详细记录:</b>\n" + "\n".join(history_message) + "\n"
            )

        return rug_message
    else:
        logging.warning(f"未能获取到地址 {address} 的 Rug 数据。")
        return "<b>Rug 状态:</b> 无法获取 Rug 数据。\n"

def filter_bluechip_tokens(tokens, min_volume=1_000_000,min_liquidity_threshold=30000,min_holder_growth_threshold=200,max_top_10_holder_rate = 0.7):
    """
    格式化代币信息以发送到 Telegram，并筛选出可能启动的 Meme 币。
    :param tokens: 从接口获取的代币数据列表。
    :param min_volume_threshold: 最小交易量阈值（美元）。
    :param min_liquidity_threshold: 最小流动性阈值（美元）。
    :param min_holder_growth_threshold: 最小持币地址增长阈值。
    :param min_price_change_1h: 最小 1 小时价格涨幅阈值（%）。
    :param max_top_10_holder_rate: 最大前 10 持币比例阈值（0-1）。
    :return: 格式化后的消息列表。
    """
    filtered_tokens = []
    for token in tokens:
        try:
            # 提取相关字段
            volume = float(token.get("volume", 0))
            price_change_1m = float(token.get("price_change_percent_1m", 0))
            price_change_5m = float(token.get("price_change_percent_5m", 0))
            liquidity = float(token.get('liquidity', 0)) 
            holder_count = int(token.get('holder_count', 0))
            top_10_holder_rate = float(token.get('security_info', {}).get('top_10_holder_rate', 0))
                # 检查前 10 持币比例是否过高
            if top_10_holder_rate > max_top_10_holder_rate:
                continue
          
            # 筛选条件
            if (
                volume >= min_volume and 
                price_change_1m >= -60 and  # 1分钟跌幅不能超过60%
                price_change_5m >= -80  
                # liquidity >= min_liquidity_threshold and  # 检查流动性是否达到阈值
                # holder_count>= min_holder_growth_threshold # 检查持币地址数是否达到阈值
            ):
                filtered_tokens.append(token)
            else:
                logging.info(f"代币 {token.get('symbol', '未知')} 被过滤: "
                             f"volume={volume}, price_change_1m={price_change_1m}, price_change_5m={price_change_5m}")
        except Exception as e:
            logging.error(f"筛选蓝筹代币时出错: {e}")
    return filtered_tokens




def fetch_and_process_smart_wallet(address):
    """
    获取聪明钱包记录并格式化成可发送的文案。
    :param address: 代币地址
    :return: 格式化的文案或无数据提示
    """
    smart_wallet_url = f"https://gmgn.ai/defi/quotation/v1/trades/sol/{address}?limit=100&maker=&tag[]=smart_degen&tag[]=pump_smart"
    
    response = fetch_page_data(smart_wallet_url)
    logging.info(f"获取聪明钱包数据成功 {response}")
    if response and 'data' in response and 'history' in response['data']:
        history = response['data']['history']
        
        # 如果没有历史记录
        if not history:
            return "没有聪明钱包买入"
        
        # 统计总的买入和卖出次数
        total_buy = 0
        total_sell = 0
        smart_wallets = set()  # 用来存储不同的聪明钱包 maker

        for record in history:
            maker = record.get('maker')
            event = record.get('event')
            
            smart_wallets.add(maker)  # 将每个 maker 加入 set，确保每个聪明钱包只计算一次
            
            if event == 'buy':
                total_buy += 1
            elif event == 'sell':
                total_sell += 1
        
        # 构建返回的文案
        message = (
            f"总聪明钱包数量: {len(smart_wallets)}\n"
            f"总买入次数: {total_buy}\n"
            f"总卖出次数: {total_sell}\n"
        )
        
        return message
    
    return "获取聪明钱包数据失败，或数据格式不正确。"


def ca_format_tg_message(token_info):
    """
    将代币信息格式化为 Telegram 消息
    """
    top_10_holder_rate = token_info.get('top_10_holder_rate', '未知')
    message = (
        f"📈 代币名称: {token_info.get('name', '未知')}\n"
        f"💰 代币符号: {token_info.get('symbol', '未知')}\n"
        f"🔗 地址: {token_info.get('address', '未知')}\n"
        f"📊 **前 10 名持有者占比**: {top_10_holder_rate if top_10_holder_rate else '无持有者信息'}\n"
        f"📊 流通供应量: {token_info.get('circulating_supply', '未知')}\n"
        f"📈 总供应量: {token_info.get('total_supply', '未知')}\n"
        f"💵 当前价格: {token_info.get('price', '未知')}\n"
        f"👥 持有者数量: {token_info.get('holder_count', '未知')}\n"
        f"🔒 流动性: {token_info.get('liquidity', '未知')}\n"
        f"📊 池地址: {token_info.get('biggest_pool_address', '未知')}\n"
        # f"🕒 交易信息:\n"
        # f"    1m: 买数: {token_info.get('buys_1m', '未知')}, 卖数: {token_info.get('sells_1m', '未知')}, 交易量: {token_info.get('volume_1m', '未知')}, 买入量: {token_info.get('buy_volume_1m', '未知')}\n"
        # f"    5m: 买数: {token_info.get('buys_5m', '未知')}, 卖数: {token_info.get('sells_5m', '未知')}, 交易量: {token_info.get('volume_5m', '未知')}, 买入量: {token_info.get('buy_volume_5m', '未知')}\n"
        # f"    1h: 买数: {token_info.get('buys_1h', '未知')}, 卖数: {token_info.get('sells_1h', '未知')}, 交易量: {token_info.get('volume_1h', '未知')}, 买入量: {token_info.get('buy_volume_1h', '未知')}\n"
        # f"    6h: 买数: {token_info.get('buys_6h', '未知')}, 卖数: {token_info.get('sells_6h', '未知')}, 交易量: {token_info.get('volume_6h', '未知')}, 买入量: {token_info.get('buy_volume_6h', '未知')}\n"
        # f"    24h: 买数: {token_info.get('buys_24h', '未知')}, 卖数: {token_info.get('sells_24h', '未知')}, 交易量: {token_info.get('volume_24h', '未知')}, 买入量: {token_info.get('buy_volume_24h', '未知')}\n"
           
        f"交易汇总信息\n"
        f"1m：\n"
        f"总笔数汇总: {token_info.get('buy_count_1m', 0) - token_info.get('sell_count_1m', 0)}\n"
        f"总交易金额: {token_info.get('buy_volume_1m', 0) - token_info.get('sell_volume_1m', 0)}\n\n"
        
        f"5m：\n"
        f"笔数汇总: {token_info.get('buy_count_5m', 0) - token_info.get('sell_count_5m', 0)}\n"
        f"总交易金额: {token_info.get('buy_volume_5m', 0) - token_info.get('sell_volume_5m', 0)}\n\n"
        
        f"1h：\n"
        f"笔数汇总: {token_info.get('buy_count_1h', 0) - token_info.get('sell_count_1h', 0)}\n"
        f"总交易金额: {token_info.get('buy_volume_1h', 0) - token_info.get('sell_volume_1h', 0)}\n\n"
        
        f"6h：\n"
        f"笔数汇总: {token_info.get('buy_count_6h', 0) - token_info.get('sell_count_6h', 0)}\n"
        f"总交易金额: {token_info.get('buy_volume_6h', 0) - token_info.get('sell_volume_6h', 0)}\n\n"
        
        f"24h：\n"
        f"笔数汇总: {token_info.get('buy_count_24h', 0) - token_info.get('sell_count_24h', 0)}\n"
        f"总交易金额: {token_info.get('buy_volume_24h', 0) - token_info.get('sell_volume_24h', 0)}\n"
    )
    return message
