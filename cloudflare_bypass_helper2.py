import os
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
import logging
import json
from CloudflareBypasser import CloudflareBypasser

def get_chromium_options(browser_path: str, arguments: list):
    """
    配置并返回 Chromium 浏览器选项。

    :param browser_path: Chromium 浏览器可执行文件的路径。
    :param arguments: 浏览器的启动参数列表。
    :return: 配置好的 ChromiumOptions 实例。
    """
    options = ChromiumOptions()
    options.set_paths(browser_path=browser_path)
    for argument in arguments:
        options.set_argument(argument)
    return options

def fetch_page_data(url):
    """
    绕过 Cloudflare 并提取页面数据。
    """
      # 判断是否为无头模式
    is_headless = os.getenv('HEADLESS', 'false').lower() == 'true'
    if is_headless:
        from pyvirtualdisplay import Display
        display = Display(visible=0, size=(1920, 1080))
        display.start()

    # 设置 Chromium 浏览器路径（根据操作系统调整路径）
    browser_path = os.getenv('CHROME_PATH', "/usr/bin/google-chrome")

    # 浏览器启动参数
    arguments = [
        "-no-first-run",
        "-force-color-profile=srgb",
        "-metrics-recording-only",
        "-password-store=basic",
        "-use-mock-keychain",
        "-no-default-browser-check",
        "-disable-background-mode",
        "-enable-features=NetworkService,NetworkServiceInProcess",
        "-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",
        "-deny-permission-prompts",
        "-disable-gpu",
        "-accept-lang=en-US",
    ]

    # 配置 Chromium 浏览器
    options = get_chromium_options(browser_path, arguments)

    # 初始化浏览器
    driver = ChromiumPage(addr_or_opts=options)

    try:
        logging.info('Navigating to the demo page.')
        driver.get(url)

        # 开始绕过 Cloudflare
        logging.info('Starting Cloudflare bypass.')
        cf_bypasser = CloudflareBypasser(driver)
        cf_bypasser.bypass()

        # 成功绕过后获取页面内容
        logging.info("Enjoy the content!")
        logging.info("Title of the page: %s", driver.title)

        # 从 driver.html 提取页面 HTML
        page_source = driver.html

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(page_source, 'html.parser')

        # 查找 <pre> 标签，提取 JSON 数据
        pre_content = soup.find('pre')
        if pre_content:
            json_data = pre_content.text  # 提取 JSON 文本
            parsed_data = json.loads(json_data)  # 解析 JSON 数据
            logging.info("JSON data extracted successfully.")
             # 打印 JSON 数据到控制台
           # print(json.dumps(parsed_data, indent=4, ensure_ascii=False))

            # 保存 JSON 数据到文件
            output_file = 'extracted_data.json'
            with open(output_file, 'w', encoding='utf-8') as file:
                json.dump(parsed_data, file, indent=4, ensure_ascii=False)
                logging.info(f"JSON data saved to {output_file}")
            return parsed_data
        else:
            logging.error("Failed to extract JSON data.")
            return None
    finally:
        logging.info("Closing the browser.")
        driver.quit()
        if is_headless:
            display.stop()
